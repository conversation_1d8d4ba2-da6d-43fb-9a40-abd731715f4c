<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CUFE Email Processor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .result.json {
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .advanced {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .advanced h3 {
            margin-top: 0;
        }
        .results-container {
            margin-top: 20px;
        }
        .summary-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .summary-card h3 {
            margin-top: 0;
            color: #495057;
        }
        .stat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
        }
        .cufe-list {
            margin-top: 20px;
        }
        .cufe-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .cufe-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .cufe-value {
            font-family: monospace;
            font-weight: bold;
            color: #28a745;
            font-size: 14px;
        }
        .cufe-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .cufe-detail {
            font-size: 14px;
        }
        .cufe-detail strong {
            color: #495057;
        }
        .download-section {
            margin-top: 20px;
            padding: 15px;
            background: #e7f3ff;
            border-radius: 8px;
            border: 1px solid #b3d9ff;
        }
        .download-btn {
            background-color: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .download-btn:hover {
            background-color: #218838;
        }
        .toggle-json {
            background-color: #6c757d;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }
        .toggle-json:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 CUFE Email Processor</h1>
        
        <form id="emailForm">
            <div class="form-group">
                <label for="email_host">Email Server (IMAP Host):</label>
                <input type="text" id="email_host" name="email_host" value="imap.gmail.com" required>
            </div>
            
            <div class="form-group">
                <label for="email_port">Port:</label>
                <input type="number" id="email_port" name="email_port" value="993" required>
            </div>
            
            <div class="form-group">
                <label for="email_username">Email Address:</label>
                <input type="email" id="email_username" name="email_username" required>
            </div>
            
            <div class="form-group">
                <label for="email_password">Password (App Password for Gmail):</label>
                <input type="password" id="email_password" name="email_password" required>
            </div>
            
            <div class="form-group">
                <label for="use_ssl">Use SSL:</label>
                <select id="use_ssl" name="use_ssl">
                    <option value="true">Yes (Recommended)</option>
                    <option value="false">No</option>
                </select>
            </div>
            
            <div class="advanced">
                <h3>Advanced Options</h3>
                
                <div class="form-group">
                    <label for="folder">Folder:</label>
                    <input type="text" id="folder" name="folder" value="INBOX">
                </div>
                
                <div class="form-group">
                    <label for="max_emails">Max Emails to Process:</label>
                    <input type="number" id="max_emails" name="max_emails" value="10" min="1" max="100">
                </div>
                
                <div class="form-group">
                    <label for="date_filter">Date Filter (optional):</label>
                    <input type="text" id="date_filter" name="date_filter" placeholder="e.g., SINCE 01-Jan-2024">
                </div>
            </div>
            
            <button type="submit" id="processBtn">🚀 Process Emails</button>
            <button type="button" id="testConnectionBtn">🔗 Test Connection</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // Detect if running in Docker or locally
        const API_BASE_URL = window.location.hostname === 'localhost' && window.location.port === '3000'
            ? 'http://localhost:8000'  // Running locally
            : 'http://localhost:8000'; // Running in Docker
        
        document.getElementById('emailForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await processEmails();
        });
        
        document.getElementById('testConnectionBtn').addEventListener('click', async function() {
            await testConnection();
        });
        
        async function processEmails() {
            const formData = new FormData(document.getElementById('emailForm'));
            const data = Object.fromEntries(formData.entries());
            
            // Convert string values to appropriate types
            data.email_port = parseInt(data.email_port);
            data.use_ssl = data.use_ssl === 'true';
            data.max_emails = parseInt(data.max_emails);
            
            // Remove empty date_filter
            if (!data.date_filter) {
                delete data.date_filter;
            }
            
            showResult('Processing emails... This may take a few minutes.', 'loading');
            setButtonsDisabled(true);
            
            try {
                const response = await fetch(`${API_BASE_URL}/process-emails-sync`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();

                if (response.ok) {
                    showProcessingResults(result);
                } else {
                    showResult(`❌ Error: ${result.detail || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Network Error: ${error.message}`, 'error');
            } finally {
                setButtonsDisabled(false);
            }
        }
        
        async function testConnection() {
            showResult('Testing API connection...', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const result = await response.json();
                
                if (response.ok) {
                    showResult(`✅ API Connection Successful!\n\nService: ${result.service || 'Unknown'}\nStatus: ${result.status || 'Unknown'}`, 'success');
                } else {
                    showResult(`❌ API Error: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Connection Failed: ${error.message}\n\nMake sure the API server is running on ${API_BASE_URL}`, 'error');
            }
        }
        
        function showProcessingResults(data) {
            const resultDiv = document.getElementById('result');
            const result = data.result || {};

            // Extract key information
            const processedCount = result.processed_count || 0;
            const downloadedFiles = result.downloaded_files || [];
            const cufeExtractions = result.cufe_extractions || [];
            const extractedXmlFiles = result.extracted_xml_files || [];

            let html = `
                <div class="results-container">
                    <div class="summary-card">
                        <h3>📊 Processing Summary</h3>
                        <div class="stat-grid">
                            <div class="stat-item">
                                <div class="stat-number">${processedCount}</div>
                                <div class="stat-label">Emails Processed</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${downloadedFiles.length}</div>
                                <div class="stat-label">ZIP Files Downloaded</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${extractedXmlFiles.length}</div>
                                <div class="stat-label">XML Files Extracted</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">${cufeExtractions.length}</div>
                                <div class="stat-label">CUFE Values Found</div>
                            </div>
                        </div>
                    </div>
            `;

            // Show downloaded files with download links
            if (downloadedFiles.length > 0) {
                html += `
                    <div class="download-section">
                        <h3>📁 Downloaded ZIP Files</h3>
                        <p>Click to download the original ZIP files:</p>
                `;

                downloadedFiles.forEach(file => {
                    html += `
                        <a href="#" onclick="downloadFile('${file.file_path}', '${file.filename}')" class="download-btn">
                            📦 ${file.filename} (${formatFileSize(file.file_size)})
                        </a>
                    `;
                });

                html += `</div>`;
            }

            // Show CUFE extractions
            if (cufeExtractions.length > 0) {
                html += `
                    <div class="cufe-list">
                        <h3>🔍 Extracted CUFE Information</h3>
                `;

                cufeExtractions.forEach((cufe, index) => {
                    html += `
                        <div class="cufe-item">
                            <div class="cufe-header">
                                <div class="cufe-value">CUFE: ${cufe.cufe_value}</div>
                                <small>From: ${cufe.xml_file}</small>
                            </div>
                            <div class="cufe-details">
                                ${cufe.issuer_name ? `<div class="cufe-detail"><strong>Issuer:</strong> ${cufe.issuer_name}</div>` : ''}
                                ${cufe.document_number ? `<div class="cufe-detail"><strong>Document:</strong> ${cufe.document_number}</div>` : ''}
                                ${cufe.total_amount ? `<div class="cufe-detail"><strong>Amount:</strong> ${cufe.total_amount}</div>` : ''}
                                ${cufe.issue_date ? `<div class="cufe-detail"><strong>Issue Date:</strong> ${formatDate(cufe.issue_date)}</div>` : ''}
                                ${cufe.extraction_date ? `<div class="cufe-detail"><strong>Extracted:</strong> ${formatDate(cufe.extraction_date)}</div>` : ''}
                            </div>
                        </div>
                    `;
                });

                html += `</div>`;
            } else if (extractedXmlFiles.length > 0) {
                html += `
                    <div class="summary-card">
                        <h3>⚠️ No CUFE Found</h3>
                        <p>XML files were extracted but no CUFE values were found. This might indicate:</p>
                        <ul>
                            <li>The XML files are not Colombian electronic invoices</li>
                            <li>The XML structure doesn't match the expected UBL 2.1 format</li>
                            <li>The CUFE element is missing or has a different structure</li>
                        </ul>
                    </div>
                `;
            }

            // Add toggle for raw JSON
            html += `
                <button class="toggle-json" onclick="toggleRawJson()">Show Raw JSON</button>
                <div id="rawJson" style="display: none;" class="result json success">
                    ${JSON.stringify(data, null, 2)}
                </div>
            `;

            html += `</div>`;

            resultDiv.innerHTML = html;
            resultDiv.className = 'result success';
            resultDiv.style.display = 'block';
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        function setButtonsDisabled(disabled) {
            document.getElementById('processBtn').disabled = disabled;
            document.getElementById('testConnectionBtn').disabled = disabled;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
            } catch (e) {
                return dateString;
            }
        }

        async function downloadFile(filePath, filename) {
            try {
                showResult('Preparing download...', 'loading');

                // Create a download endpoint call
                const response = await fetch(`${API_BASE_URL}/download/zip/${encodeURIComponent(filePath)}`, {
                    method: 'GET'
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showResult(`✅ Download started: ${filename}`, 'success');
                } else {
                    // Fallback: show file path for manual access
                    showResult(`📁 File location: ${filePath}\n\nNote: Direct download not available. You can access the file at the path shown above.`, 'loading');
                }
            } catch (error) {
                showResult(`📁 File location: ${filePath}\n\nNote: Direct download failed, but you can access the file at the path shown above.`, 'loading');
            }
        }

        function toggleRawJson() {
            const rawJsonDiv = document.getElementById('rawJson');
            const button = event.target;

            if (rawJsonDiv.style.display === 'none') {
                rawJsonDiv.style.display = 'block';
                button.textContent = 'Hide Raw JSON';
            } else {
                rawJsonDiv.style.display = 'none';
                button.textContent = 'Show Raw JSON';
            }
        }

        // Test connection on page load
        window.addEventListener('load', function() {
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
