import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { LoginRequest, LoginResponse, User, Client } from '../types/auth'

// API Base URL - adjust based on environment
const API_BASE_URL = import.meta.env.VITE_API_URL || '/api'

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response: AxiosResponse<LoginResponse> = await apiClient.post('/auth/login', credentials)
    return response.data
  },

  getCurrentUser: async (token?: string): Promise<{ user: User; client: Client }> => {
    const headers = token ? { Authorization: `Bearer ${token}` } : {}
    const response: AxiosResponse<{ user: User; client: Client }> = await apiClient.get('/auth/me', { headers })
    return response.data
  },
}

// CUFE API types
export interface CUFERecord {
  cufe_value: string
  email_id: string
  reception_date: string
  xml_file_path: string
  pdf_file_path?: string
  processed_date: string
  issuer_name?: string
  document_number?: string
  issue_date?: string
  total_amount?: string
}

export interface CUFEListResponse {
  records: CUFERecord[]
  total: number
  skip: number
  limit: number
}

// CUFE API
export const cufeApi = {
  getCUFERecords: async (skip = 0, limit = 100): Promise<CUFEListResponse> => {
    const response: AxiosResponse<CUFEListResponse> = await apiClient.get('/cufe/', {
      params: { skip, limit }
    })
    return response.data
  },

  getCUFEById: async (cufeId: string): Promise<CUFERecord> => {
    const response: AxiosResponse<CUFERecord> = await apiClient.get(`/cufe/${cufeId}`)
    return response.data
  },
}

// Health check
export const healthApi = {
  check: async (): Promise<{ status: string; service: string }> => {
    const response = await apiClient.get('/health')
    return response.data
  },
}

export default apiClient
