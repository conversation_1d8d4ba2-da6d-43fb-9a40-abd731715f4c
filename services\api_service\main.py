"""
Main FastAPI REST API Service
Orchestrates all microservices and provides public API endpoints
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
import httpx
from typing import List, Optional
from urllib.parse import unquote
from sqlalchemy.orm import Session

from shared.schemas.api import ProcessEmailsRequest, CUFEResponse, CUFEListResponse
from shared.schemas.mailbox import EmailProcessRequest, EmailProcessResponse
from shared.database.connection import get_db, create_tables
from shared.database.models import CUFERecord, EmailRecord
from shared.utils.logger import get_logger
from shared.utils.auth import verify_token, create_user_token

# Initialize FastAPI app
app = FastAPI(
    title="CUFE Extraction API",
    description="Main API for CUFE extraction automation system",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger = get_logger(__name__)
security = HTTPBearer(auto_error=False)

# Service URLs
MAILBOX_SERVICE_URL = os.getenv("MAILBOX_SERVICE_URL", "http://localhost:8001")
FILE_PROCESSING_SERVICE_URL = os.getenv("FILE_PROCESSING_SERVICE_URL", "http://localhost:8002")
EXTRACTION_SERVICE_URL = os.getenv("EXTRACTION_SERVICE_URL", "http://localhost:8003")

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    """Create database tables on startup"""
    try:
        create_tables()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {str(e)}")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "api-service"}

# Authentication endpoints
@app.post("/auth/login")
async def login(username: str, password: str):
    """
    Simple login endpoint for demo purposes
    In production, implement proper user authentication
    """
    # For demo purposes, accept any non-empty credentials
    if username and password:
        token = create_user_token(username)
        return {
            "access_token": token,
            "token_type": "bearer",
            "username": username
        }
    else:
        raise HTTPException(status_code=401, detail="Invalid credentials")

@app.get("/auth/me")
async def get_current_user_info(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user information"""
    if not credentials:
        raise HTTPException(status_code=401, detail="Authentication required")

    try:
        user = verify_token(credentials.credentials)
        return {
            "username": user.get("sub"),
            "user_id": user.get("user_id")
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid token")

# Main API endpoints
@app.post("/process-emails")
async def process_emails(
    request: ProcessEmailsRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Trigger the complete email processing pipeline
    """
    try:
        # Verify JWT token (optional for demo)
        user = None
        if credentials:
            try:
                user = verify_token(credentials.credentials)
                logger.info(f"Processing emails requested by user: {user.get('sub')}")
            except:
                logger.warning("Invalid token provided, proceeding without authentication")

        # Add to background tasks for async processing
        background_tasks.add_task(process_emails_pipeline, request, db)

        return {
            "message": "Email processing started",
            "status": "processing",
            "user": user.get("sub") if user else "anonymous"
        }

    except Exception as e:
        logger.error(f"Error starting email processing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.post("/process-emails-sync")
async def process_emails_sync(
    request: ProcessEmailsRequest,
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Process emails synchronously (for frontend testing)
    """
    try:
        # Verify JWT token (optional for demo)
        user = None
        if credentials:
            try:
                user = verify_token(credentials.credentials)
                logger.info(f"Sync processing emails requested by user: {user.get('sub')}")
            except:
                logger.warning("Invalid token provided, proceeding without authentication")

        # Call mailbox service directly
        async with httpx.AsyncClient() as client:
            # Convert to mailbox service request format
            mailbox_request = EmailProcessRequest(
                email_host=request.email_host,
                email_port=request.email_port,
                email_username=request.email_username,
                email_password=request.email_password,
                use_ssl=request.use_ssl,
                folder=request.folder,
                date_filter=request.date_filter,
                max_emails=request.max_emails
            )

            # Call mailbox service
            response = await client.post(
                f"{MAILBOX_SERVICE_URL}/process-emails",
                json=mailbox_request.dict(),
                timeout=300.0  # 5 minutes timeout
            )

            if response.status_code == 200:
                result = response.json()

                # Process the complete pipeline for sync response
                downloaded_files = result.get('downloaded_files', [])
                all_xml_files = []
                cufe_results = []

                # Process each downloaded ZIP file
                for downloaded_file in downloaded_files:
                    try:
                        # Process ZIP file
                        file_process_response = await client.post(
                            f"{FILE_PROCESSING_SERVICE_URL}/process-zip",
                            json={
                                "file_path": downloaded_file['file_path'],
                                "email_id": downloaded_file.get('email_id'),
                                "preserve_structure": True
                            },
                            timeout=120.0
                        )

                        if file_process_response.status_code == 200:
                            file_result = file_process_response.json()
                            xml_files = file_result.get('xml_files', [])
                            all_xml_files.extend(xml_files)

                            # Extract CUFE from each XML file
                            for xml_file in xml_files:
                                try:
                                    extraction_response = await client.post(
                                        f"{EXTRACTION_SERVICE_URL}/extract-cufe",
                                        json={
                                            "xml_file_path": xml_file['file_path'],
                                            "email_id": xml_file.get('email_id'),
                                            "extract_additional_data": True
                                        },
                                        timeout=60.0
                                    )

                                    if extraction_response.status_code == 200:
                                        extraction_result = extraction_response.json()
                                        if extraction_result.get('success') and extraction_result.get('cufe_value'):
                                            cufe_results.append({
                                                "cufe_value": extraction_result['cufe_value'],
                                                "xml_file": xml_file['filename'],
                                                "issuer_name": extraction_result.get('cufe_data', {}).get('issuer_name'),
                                                "document_number": extraction_result.get('cufe_data', {}).get('document_number'),
                                                "issue_date": extraction_result.get('cufe_data', {}).get('issue_date'),
                                                "total_amount": extraction_result.get('cufe_data', {}).get('total_amount'),
                                                "extraction_date": extraction_result.get('extraction_date')
                                            })
                                except Exception as e:
                                    logger.error(f"Error extracting CUFE from {xml_file['filename']}: {str(e)}")

                    except Exception as e:
                        logger.error(f"Error processing file {downloaded_file['filename']}: {str(e)}")

                # Enhanced response with CUFE information
                enhanced_result = result.copy()
                enhanced_result['extracted_xml_files'] = all_xml_files
                enhanced_result['cufe_extractions'] = cufe_results
                enhanced_result['cufe_count'] = len(cufe_results)

                return {
                    "message": "Email processing completed",
                    "status": "completed",
                    "result": enhanced_result,
                    "user": user.get("sub") if user else "anonymous"
                }
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Mailbox service error: {response.text}"
                )

    except httpx.RequestError as e:
        logger.error(f"Error calling mailbox service: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Mailbox service unavailable: {str(e)}")
    except Exception as e:
        logger.error(f"Error in sync email processing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.get("/cufe/{cufe_id}", response_model=CUFEResponse)
async def get_cufe(
    cufe_id: str,
    db = Depends(get_db)
):
    """
    Get CUFE information by ID
    """
    try:
        # TODO: Query database for CUFE record
        # cufe_record = db.query(CUFERecord).filter(CUFERecord.cufe_value == cufe_id).first()
        
        # Placeholder response
        return CUFEResponse(
            cufe_value=cufe_id,
            email_id="placeholder",
            reception_date="2024-01-01T00:00:00",
            xml_file_path="",
            pdf_file_path="",
            processed_date="2024-01-01T00:00:00"
        )
        
    except Exception as e:
        logger.error(f"Error retrieving CUFE {cufe_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CUFE retrieval failed: {str(e)}")

@app.get("/cufe/", response_model=CUFEListResponse)
async def list_cufe_records(
    skip: int = 0,
    limit: int = 100,
    db = Depends(get_db)
):
    """
    List all processed CUFE records
    """
    try:
        # TODO: Query database for CUFE records with pagination
        # records = db.query(CUFERecord).offset(skip).limit(limit).all()
        
        # Placeholder response
        return CUFEListResponse(
            records=[],
            total=0,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"Error listing CUFE records: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CUFE listing failed: {str(e)}")

@app.get("/download/{file_type}/{cufe_id}")
async def download_file(
    file_type: str,
    cufe_id: str,
    db = Depends(get_db)
):
    """
    Download XML or PDF file associated with a CUFE
    """
    try:
        if file_type not in ["xml", "pdf"]:
            raise HTTPException(status_code=400, detail="File type must be 'xml' or 'pdf'")
        
        # TODO: Get file path from database and return file
        # cufe_record = db.query(CUFERecord).filter(CUFERecord.cufe_value == cufe_id).first()
        
        # Placeholder - return error for now
        raise HTTPException(status_code=404, detail="File not found")
        
    except Exception as e:
        logger.error(f"Error downloading file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"File download failed: {str(e)}")

# Background task function
async def process_emails_pipeline(request: ProcessEmailsRequest, db: Session):
    """
    Background task to process emails through the pipeline
    """
    try:
        logger.info("Starting email processing pipeline")

        # Step 1: Call mailbox service
        async with httpx.AsyncClient() as client:
            mailbox_request = EmailProcessRequest(
                email_host=request.email_host,
                email_port=request.email_port,
                email_username=request.email_username,
                email_password=request.email_password,
                use_ssl=request.use_ssl,
                folder=request.folder,
                date_filter=request.date_filter,
                max_emails=request.max_emails
            )

            try:
                response = await client.post(
                    f"{MAILBOX_SERVICE_URL}/process-emails",
                    json=mailbox_request.dict(),
                    timeout=300.0
                )

                if response.status_code == 200:
                    mailbox_result = response.json()
                    logger.info(f"Mailbox processing completed: {mailbox_result}")

                    # Step 2: Call file processing service for each downloaded file
                    downloaded_files = mailbox_result.get('downloaded_files', [])
                    all_xml_files = []

                    for downloaded_file in downloaded_files:
                        try:
                            # Process ZIP file
                            file_process_response = await client.post(
                                f"{FILE_PROCESSING_SERVICE_URL}/process-zip",
                                json={
                                    "file_path": downloaded_file['file_path'],
                                    "email_id": downloaded_file.get('email_id'),
                                    "preserve_structure": True
                                },
                                timeout=120.0
                            )

                            if file_process_response.status_code == 200:
                                file_result = file_process_response.json()
                                xml_files = file_result.get('xml_files', [])
                                all_xml_files.extend(xml_files)
                                logger.info(f"Processed ZIP file {downloaded_file['filename']}: {len(xml_files)} XML files extracted")
                            else:
                                logger.error(f"File processing failed for {downloaded_file['filename']}: {file_process_response.text}")

                        except Exception as e:
                            logger.error(f"Error processing file {downloaded_file['filename']}: {str(e)}")

                    # Step 3: Call extraction service for each XML file
                    cufe_results = []

                    for xml_file in all_xml_files:
                        try:
                            # Extract CUFE from XML file
                            extraction_response = await client.post(
                                f"{EXTRACTION_SERVICE_URL}/extract-cufe",
                                json={
                                    "xml_file_path": xml_file['file_path'],
                                    "email_id": xml_file.get('email_id'),
                                    "extract_additional_data": True
                                },
                                timeout=60.0
                            )

                            if extraction_response.status_code == 200:
                                extraction_result = extraction_response.json()
                                if extraction_result.get('success') and extraction_result.get('cufe_value'):
                                    cufe_results.append(extraction_result)
                                    logger.info(f"Extracted CUFE from {xml_file['filename']}: {extraction_result['cufe_value']}")
                                else:
                                    logger.warning(f"No CUFE found in {xml_file['filename']}")
                            else:
                                logger.error(f"CUFE extraction failed for {xml_file['filename']}: {extraction_response.text}")

                        except Exception as e:
                            logger.error(f"Error extracting CUFE from {xml_file['filename']}: {str(e)}")

                    # Step 4: Results are already stored in database by the extraction service
                    logger.info(f"Pipeline completed: {len(cufe_results)} CUFE values extracted")

                else:
                    logger.error(f"Mailbox service error: {response.status_code} - {response.text}")

            except httpx.RequestError as e:
                logger.error(f"Failed to call mailbox service: {str(e)}")

        logger.info("Email processing pipeline completed")

    except Exception as e:
        logger.error(f"Error in email processing pipeline: {str(e)}")

# File download endpoints
@app.get("/download/zip/{file_path:path}")
async def download_zip_file(file_path: str):
    """
    Download a ZIP file by its file path
    """
    try:
        # Decode the URL-encoded file path
        decoded_path = unquote(file_path)

        # Validate file exists and is a ZIP file
        if not os.path.exists(decoded_path):
            raise HTTPException(status_code=404, detail=f"File not found: {decoded_path}")

        if not decoded_path.lower().endswith('.zip'):
            raise HTTPException(status_code=400, detail="Only ZIP files can be downloaded")

        # Get filename for download
        filename = os.path.basename(decoded_path)

        logger.info(f"Serving download for file: {decoded_path}")

        return FileResponse(
            path=decoded_path,
            filename=filename,
            media_type='application/zip'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving file download: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Download failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=os.getenv("DEBUG", "false").lower() == "true"
    )
